# import os
# import cv2
# import numpy as np
# from scipy.ndimage import binary_fill_holes

# class ContourBasedSegmenter:
#     def __init__(self):
#         # 类别定义
#         self.class_info = {
#             0: {"name": "bed_grounded", "room": "bedroom"},
#             1: {"name": "bed_highleg", "room": "bedroom"},
#             2: {"name": "sofa_grounded", "room": "living_room"},
#             3: {"name": "sofa_highleg", "room": "living_room"},
#             4: {"name": "door", "room": None},
#             5: {"name": "dining_table_set", "room": "kitchen"}
#         }
        
#         # 颜色映射
#         self.room_colors = {
#             "bedroom": (0, 0, 255),      # 红色
#             "living_room": (0, 255, 0),   # 绿色
#             "kitchen": (255, 0, 0),       # 蓝色
#             "door": (255, 255, 0),        # 青色
#             "unknown": (128, 128, 128)    # 灰色
#         }
        
#         # 参数配置
#         self.door_barrier_width = 5       # 门禁区域宽度
#         self.contour_sample_step = 2       # 轮廓采样步长
#         self.min_room_area = 30          # 最小房间面积
#         self.edge_smooth_size = 3          # 边缘平滑核大小

#     def load_yolo_labels(self, label_path, img_size):
#         """解析YOLO格式标签文件"""
#         detections = []
#         img_width, img_height = img_size
        
#         if not os.path.exists(label_path):
#             return detections
            
#         with open(label_path, 'r') as f:
#             for line in f:
#                 parts = line.strip().split()
#                 if len(parts) < 5:
#                     continue
                    
#                 # 解析YOLO格式
#                 class_id = int(parts[0])
#                 x_center = float(parts[1]) * img_width
#                 y_center = float(parts[2]) * img_height
#                 width = float(parts[3]) * img_width
#                 height = float(parts[4]) * img_height
                
#                 # 计算边界框
#                 x1 = int(x_center - width/2)
#                 y1 = int(y_center - height/2)
#                 x2 = int(x_center + width/2)
#                 y2 = int(y_center + height/2)
                
#                 # 确保坐标在图像范围内
#                 x1 = max(0, min(x1, img_width-1))
#                 y1 = max(0, min(y1, img_height-1))
#                 x2 = max(0, min(x2, img_width-1))
#                 y2 = max(0, min(y2, img_height-1))
                
#                 detections.append((x1, y1, x2, y2, class_id))
                
#         return detections

#     def create_contour_based_segmentation(self, detections, img_size):
#         """基于轮廓创建分割"""
#         height, width = img_size
        
#         # 初始化掩码
#         door_mask = np.zeros((height, width), dtype=np.uint8)
#         object_masks = {
#             "bedroom": np.zeros((height, width), dtype=np.uint8),
#             "living_room": np.zeros((height, width), dtype=np.uint8),
#             "kitchen": np.zeros((height, width), dtype=np.uint8)
#         }
        
#         # 1. 标记检测对象和门位置
#         for (x1, y1, x2, y2, class_id) in detections:
#             class_info = self.class_info.get(class_id)
#             if not class_info:
#                 continue
                
#             if class_info["name"] == "door":
#                 cv2.rectangle(door_mask, (x1, y1), (x2, y2), 1, -1)
#             elif class_info["room"] in object_masks:
#                 room_type = class_info["room"]
#                 cv2.rectangle(object_masks[room_type], (x1, y1), (x2, y2), 1, -1)
        
#         # 2. 创建门禁区域
#         kernel = np.ones((self.door_barrier_width, self.door_barrier_width), np.uint8)
#         door_barrier = cv2.dilate(door_mask, kernel)
        
#         # 3. 初始化分割结果
#         segmentation = np.zeros((height, width), dtype=np.uint8)
        
#         # 4. 处理每个房间类型
#         for idx, room_type in enumerate(["bedroom", "living_room", "kitchen"]):
#             # 填充对象区域
#             filled = binary_fill_holes(object_masks[room_type]).astype(np.uint8)
            
#             # 获取所有轮廓
#             contours, _ = cv2.findContours(filled, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
#             for cnt in contours:
#                 # 计算轮廓面积
#                 area = cv2.contourArea(cnt)
#                 if area < self.min_room_area:
#                     continue
                
#                 # 创建临时掩码用于当前区域
#                 region_mask = np.zeros((height, width), dtype=np.uint8)
#                 cv2.drawContours(region_mask, [cnt], -1, 1, -1)
                
#                 # 从轮廓点进行区域生长
#                 for i in range(0, len(cnt), self.contour_sample_step):
#                     x, y = cnt[i][0]
                    
#                     # 创建生长掩码 (必须比原图大2像素)
#                     grow_mask = np.zeros((height+2, width+2), dtype=np.uint8)
#                     grow_mask[1:-1, 1:-1] = door_barrier
                    
#                     # 执行区域生长
#                     cv2.floodFill(
#                         region_mask, 
#                         grow_mask, 
#                         (x, y), 
#                         1,  # 填充值
#                         flags=4 | (255 << 8)  # 4: 仅考虑4连通区域
#                     )
                
#                 # 应用门禁限制
#                 region_mask[door_barrier == 1] = 0
                
#                 # 合并到最终分割
#                 segmentation[region_mask == 1] = idx + 1
        
#         # 5. 边缘平滑处理
#         kernel = np.ones((self.edge_smooth_size, self.edge_smooth_size), np.uint8)
#         segmentation = cv2.morphologyEx(segmentation, cv2.MORPH_CLOSE, kernel)
        
#         # 6. 创建彩色可视化
#         color_map = np.zeros((height, width, 3), dtype=np.uint8)
#         for idx, room in enumerate(["bedroom", "living_room", "kitchen"]):
#             color_map[segmentation == idx+1] = self.room_colors[room]
#         color_map[door_mask == 1] = self.room_colors["door"]
#         color_map[segmentation == 0] = self.room_colors["unknown"]
        
#         return color_map

#     def process_image(self, image_path, label_path, output_path):
#         """处理单张图像"""
#         img = cv2.imread(image_path)
#         if img is None:
#             print(f"无法读取图像: {image_path}")
#             return
            
#         height, width = img.shape[:2]
#         detections = self.load_yolo_labels(label_path, (width, height))
        
#         # 创建分割掩码
#         room_mask = self.create_contour_based_segmentation(detections, (height, width))
        
#         # 可视化叠加
#         result = cv2.addWeighted(img, 0.7, room_mask, 0.3, 0)
#         cv2.imwrite(output_path, result)
#         print(f"处理完成: {image_path}")


# # 使用示例
# if __name__ == "__main__":
#     # 配置路径
#     IMAGE_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/test"
#     LABEL_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/test"
#     OUTPUT_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/room_masks"
    
#     os.makedirs(OUTPUT_DIR, exist_ok=True)
    
#     # 创建分割器
#     segmenter = ContourBasedSegmenter()
    
#     # 处理所有图像
#     for img_name in os.listdir(IMAGE_DIR):
#         if img_name.lower().endswith(('.png', '.jpg', '.jpeg')):
#             img_path = os.path.join(IMAGE_DIR, img_name)
#             label_name = os.path.splitext(img_name)[0] + '.txt'
#             label_path = os.path.join(LABEL_DIR, label_name)
#             output_path = os.path.join(OUTPUT_DIR, f"seg_{img_name}")
            
#             segmenter.process_image(img_path, label_path, output_path)



import os
import cv2
import numpy as np
from scipy.ndimage import binary_fill_holes

class PixelEdgeSegmenter:
    def __init__(self):
        # 类别定义
        self.class_info = {
            0: {"name": "bed_grounded", "room": "bedroom"},
            1: {"name": "bed_highleg", "room": "bedroom"},
            2: {"name": "sofa_grounded", "room": "living_room"},
            3: {"name": "sofa_highleg", "room": "living_room"},
            4: {"name": "door", "room": None},
            5: {"name": "dining_table_set", "room": "kitchen"}
        }
        
        # 颜色映射
        self.room_colors = {
            "bedroom": (0, 0, 255),      # 红色
            "living_room": (0, 255, 0),  # 绿色
            "kitchen": (255, 0, 0),      # 蓝色
            "door": (255, 255, 0),       # 青色
            "unknown": (128, 128, 128)  # 灰色
        }
        
        # 参数配置
        self.edge_threshold1 = 50       # Canny边缘检测低阈值
        self.edge_threshold2 = 150      # Canny边缘检测高阈值
        self.door_barrier_width = 25     # 门禁区域宽度
        self.region_growth_threshold = 30 # 区域生长像素差异阈值

    def detect_room_edges(self, image):
        """检测房间边缘"""
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 使用Canny检测边缘
        edges = cv2.Canny(gray, self.edge_threshold1, self.edge_threshold2)
        
        # 膨胀边缘使其更连续
        kernel = np.ones((3, 3), np.uint8)
        edges = cv2.dilate(edges, kernel, iterations=1)
        
        return edges

    def create_pixel_based_segmentation(self, image, detections):
        """基于像素边缘创建分割"""
        height, width = image.shape[:2]
        
        # 1. 检测图像中的边缘
        edge_map = self.detect_room_edges(image)
        
        # 2. 创建门掩码和对象掩码
        door_mask = np.zeros((height, width), dtype=np.uint8)
        object_masks = {
            "bedroom": np.zeros((height, width), dtype=np.uint8),
            "living_room": np.zeros((height, width), dtype=np.uint8),
            "kitchen": np.zeros((height, width), dtype=np.uint8)
        }
        
        for (x1, y1, x2, y2, class_id) in detections:
            class_info = self.class_info.get(class_id)
            if not class_info:
                continue
                
            if class_info["name"] == "door":
                cv2.rectangle(door_mask, (x1, y1), (x2, y2), 1, -1)
            elif class_info["room"] in object_masks:
                room_type = class_info["room"]
                cv2.rectangle(object_masks[room_type], (x1, y1), (x2, y2), 1, -1)
        
        # 3. 创建门禁区域
        kernel = np.ones((self.door_barrier_width, self.door_barrier_width), np.uint8)
        door_barrier = cv2.dilate(door_mask, kernel)
        
        # 4. 创建组合边界图 (边缘+门禁)
        combined_boundaries = np.maximum(edge_map, door_barrier*255)
        
        # 5. 初始化分割结果
        segmentation = np.zeros((height, width), dtype=np.uint8)
        
        # 6. 为每个房间类型进行区域生长
        for idx, room_type in enumerate(["bedroom", "living_room", "kitchen"]):
            # 获取对象区域作为种子
            seeds = binary_fill_holes(object_masks[room_type]).astype(np.uint8)
            
            # 创建生长掩码 (边界为障碍)
            growth_mask = (combined_boundaries == 0).astype(np.uint8) * 255
            
            # 执行区域生长
            temp_mask = seeds.copy()
            cv2.floodFill(
                temp_mask, 
                growth_mask.copy(),  # 必须复制，因为会被修改
                (0, 0),  # 从左上角开始
                0,       # 填充值
                loDiff=0,
                upDiff=0,
                flags=4 | (255 << 8))
            
            # 反转得到生长区域
            room_region = 1 - temp_mask
            
            # 合并到最终分割
            segmentation[room_region == 1] = idx + 1
        
        # 7. 后处理
        segmentation[door_barrier == 1] = 0  # 清除门禁区域
        
        # 8. 创建彩色可视化
        color_map = np.zeros((height, width, 3), dtype=np.uint8)
        for idx, room in enumerate(["bedroom", "living_room", "kitchen"]):
            color_map[segmentation == idx+1] = self.room_colors[room]
        color_map[door_mask == 1] = self.room_colors["door"]
        color_map[segmentation == 0] = self.room_colors["unknown"]
        
        return color_map

    def process_image(self, image_path, label_path, output_path):
        """处理单张图像"""
        img = cv2.imread(image_path)
        if img is None:
            print(f"无法读取图像: {image_path}")
            return
            
        height, width = img.shape[:2]
        detections = self.load_yolo_labels(label_path, (width, height))
        
        # 创建分割掩码
        room_mask = self.create_pixel_based_segmentation(img, detections)
        
        # 可视化叠加
        result = cv2.addWeighted(img, 0.7, room_mask, 0.3, 0)
        cv2.imwrite(output_path, result)
        print(f"处理完成: {image_path}")

    def load_yolo_labels(self, label_path, img_size):
        """解析YOLO格式标签文件"""
        detections = []
        img_width, img_height = img_size
        
        if not os.path.exists(label_path):
            return detections
            
        with open(label_path, 'r') as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) < 5:
                    continue
                    
                # 解析YOLO格式
                class_id = int(parts[0])
                x_center = float(parts[1]) * img_width
                y_center = float(parts[2]) * img_height
                width = float(parts[3]) * img_width
                height = float(parts[4]) * img_height
                
                # 计算边界框
                x1 = int(x_center - width/2)
                y1 = int(y_center - height/2)
                x2 = int(x_center + width/2)
                y2 = int(y_center + height/2)
                
                # 确保坐标在图像范围内
                x1 = max(0, min(x1, img_width-1))
                y1 = max(0, min(y1, img_height-1))
                x2 = max(0, min(x2, img_width-1))
                y2 = max(0, min(y2, img_height-1))
                
                detections.append((x1, y1, x2, y2, class_id))
                
        return detections


# 使用示例
if __name__ == "__main__":
    # 配置路径
    IMAGE_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/test"
    LABEL_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/test"
    OUTPUT_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/room_masks"
    
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # 创建分割器
    segmenter = PixelEdgeSegmenter()
    
    # 处理所有图像
    for img_name in os.listdir(IMAGE_DIR):
        if img_name.lower().endswith(('.png', '.jpg', '.jpeg')):
            img_path = os.path.join(IMAGE_DIR, img_name)
            label_name = os.path.splitext(img_name)[0] + '.txt'
            label_path = os.path.join(LABEL_DIR, label_name)
            output_path = os.path.join(OUTPUT_DIR, f"seg_{img_name}")
            
            segmenter.process_image(img_path, label_path, output_path)