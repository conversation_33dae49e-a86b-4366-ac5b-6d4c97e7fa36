
import cv2
import numpy as np
from skimage.segmentation import find_boundaries

def find_nearest_boundary_point(seed_point, boundary_coords):
    dists = np.linalg.norm(boundary_coords - np.array(seed_point), axis=1)
    idx = np.argmin(dists)
    return tuple(boundary_coords[idx])

def connect_diningtable_top_to_boundary(mask_global, x1, y1, x2):
    # 提取边界点
    boundary_map = find_boundaries(mask_global, mode="outer").astype(np.uint8)
    boundary_coords = np.argwhere(boundary_map > 0)  # [y, x] format

    # 起点：左上角和右上角
    pt1 = (x1, y1)
    pt2 = (x2, y1)

    # 找 mask_global 中与两个点最近的边界点
    nearest1 = find_nearest_boundary_point((y1, x1), boundary_coords)  # 注意: argwhere是(y,x)
    nearest2 = find_nearest_boundary_point((y1, x2), boundary_coords)

    return (nearest1[1], nearest1[0]), (nearest2[1], nearest2[0])  # 转成(x, y)

def integrate_top_edge_as_mask(mask, x1, y1, x2):
    from skimage.segmentation import find_boundaries

    # 找边界
    boundaries = find_boundaries(mask, mode="outer").astype(np.uint8)
    coords = np.argwhere(boundaries > 0)

    def find_nearest(p):
        dists = np.linalg.norm(coords - np.array([p[1], p[0]]), axis=1)
        idx = np.argmin(dists)
        return tuple(coords[idx][::-1])  # 转为(x, y)

    pt1 = find_nearest((x1, y1))
    pt2 = find_nearest((x2, y1))

    # 获取原始 mask 轮廓
    contours, _ = cv2.findContours(mask.copy(), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    if len(contours) == 0:
        raise ValueError("未找到 mask 连通域轮廓")

    contour = contours[0].squeeze()

    # 构建闭合路径：找到离 pt1 和 pt2 最近的 contour 索引
    def find_index(contour, pt):
        dists = np.linalg.norm(contour - np.array(pt), axis=1)
        return np.argmin(dists)

    i1 = find_index(contour, pt1)
    i2 = find_index(contour, pt2)

    # 确保顺序，提取路径
    if i1 < i2:
        path = contour[i1:i2 + 1]
    else:
        path = np.vstack((contour[i1:], contour[:i2 + 1]))

    # 加上 pt2 -> pt1 的直线
    bridge = np.array([pt2, pt1], dtype=np.int32)
    new_contour = np.vstack((path, bridge)).astype(np.int32)

    # 生成新 mask
    new_mask = np.zeros_like(mask)
    cv2.fillPoly(new_mask, [new_contour], 1)

    return new_mask, pt1, pt2

def get_nearest_boundary_polygon(mask, corners):
    boundaries = find_boundaries(mask, mode="outer").astype(np.uint8)
    coords = np.argwhere(boundaries > 0)  # (y, x)

    def nearest_with_fallback(corner):
        dists = np.linalg.norm(coords - np.array([corner[1], corner[0]]), axis=1)
        idx = np.argmin(dists)
        y, x = coords[idx]
        nearest_pt = (x, y)
        # 确保最近点在角点外部或相等，否则用角点
        if (x - corner[0]) ** 2 + (y - corner[1]) ** 2 < 4:  # 太近，不换
            return corner
        else:
            return nearest_pt

    # polygon = [nearest_with_fallback(corner) for corner in corners]

    polygon = []
    for corner in corners:
        nearest_pt = nearest_with_fallback(corner)
        # 强制确保包含 dining_table_set：若最近点在角点“之内”就保留原角点
        dx = nearest_pt[0] - corner[0]
        dy = nearest_pt[1] - corner[1]
        if abs(dx) <= 1 and abs(dy) <= 1:
            polygon.append(corner)  # 最近点太近，保持原始角点
        else:
            # 确保最近点比原点“更外”，否则保留角点
            if (dx >= 0 and dy >= 0) or (dx <= 0 and dy >= 0) or (dx <= 0 and dy <= 0) or (dx >= 0 and dy <= 0):
                polygon.append(nearest_pt)
            else:
                polygon.append(corner)


    # 填充 polygon
    new_mask = np.zeros_like(mask)
    cv2.fillPoly(new_mask, [np.array(polygon, dtype=np.int32)], 1)

    return new_mask, polygon


def extract_diningroom_optimized(image_path, table_yolo_label, output_path="diningroom_optimized_result.png"):
    '''
    优化的餐厅分割方法：结合语义分割和连通域约束
    1. 基于餐桌功能区域确定候选范围
    2. 在连通域内进行精确分割
    3. 确保结果既符合语义又保持连通性
    '''

    # === 读取图像 ===
    image_gray = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    h, w = image_gray.shape

    # === dining_table_set YOLO 格式标签转像素 ===
    _, cx, cy, bw, bh = table_yolo_label
    x1 = int((cx - bw / 2) * w)
    y1 = int((cy - bh / 2) * h)
    x2 = int((cx + bw / 2) * w)
    y2 = int((cy + bh / 2) * h)
    x1, y1, x2, y2 = max(0, x1), max(0, y1), min(w - 1, x2), min(h - 1, y2)
    table_width = x2 - x1
    table_height = y2 - y1
    table_center_x = (x1 + x2) // 2
    table_center_y = (y1 + y2) // 2

    # === 可通区域提取 ===
    edges = cv2.Canny(image_gray, 50, 150)
    walkable = (image_gray > 200).astype(np.uint8)
    walkable[edges > 0] = 0

    # === 步骤1: 获取包含餐桌的连通域 ===
    # 找到餐桌中心的连通域
    _, labels = cv2.connectedComponents(walkable)
    table_label = labels[table_center_y, table_center_x]
    if table_label == 0:
        raise ValueError("餐桌位置不在可通行区域内")

    connected_component = (labels == table_label).astype(np.uint8)

    # === 步骤2: 在连通域内定义餐厅的语义区域 ===
    # 计算餐厅的合理范围
    dining_radius = max(table_width, table_height) * 0.8  # 餐厅半径

    # 创建以餐桌为中心的圆形候选区域
    y_coords, x_coords = np.ogrid[:h, :w]
    distance_from_center = np.sqrt((x_coords - table_center_x) ** 2 + (y_coords - table_center_y) ** 2)
    circular_region = (distance_from_center <= dining_radius).astype(np.uint8)

    # === 步骤3: 结合连通域和语义区域 ===
    # 取连通域和圆形区域的交集作为初始餐厅区域
    initial_dining = connected_component & circular_region

    # === 步骤4: 基于距离的精细化分割 ===
    # 创建餐桌掩码
    table_mask = np.zeros_like(walkable)
    table_mask[y1:y2, x1:x2] = 1

    # 计算到餐桌的距离变换
    distance_to_table = cv2.distanceTransform(1 - table_mask, cv2.DIST_L2, 5)

    # 在初始餐厅区域内，基于距离创建权重
    weight_map = np.zeros_like(distance_to_table)
    mask_indices = initial_dining > 0
    if np.any(mask_indices):
        distances_in_region = distance_to_table[mask_indices]
        max_dist_in_region = np.max(distances_in_region)

        # 使用sigmoid函数创建平滑的权重过渡
        weight_map[mask_indices] = 1 / (1 + np.exp((distances_in_region - max_dist_in_region * 0.6) / (max_dist_in_region * 0.1)))

    # === 步骤5: 自适应阈值分割 ===
    # 结合初始区域和权重
    dining_likelihood = initial_dining.astype(np.float32) * weight_map

    # 使用自适应阈值
    if np.max(dining_likelihood) > 0:
        threshold = np.mean(dining_likelihood[dining_likelihood > 0]) * 0.7
    else:
        threshold = 0.5

    dining_mask = (dining_likelihood > threshold).astype(np.uint8)

    # === 步骤6: 形态学后处理 ===
    kernel = np.ones((3, 3), np.uint8)
    dining_mask = cv2.morphologyEx(dining_mask, cv2.MORPH_CLOSE, kernel)
    dining_mask = cv2.morphologyEx(dining_mask, cv2.MORPH_OPEN, kernel)

    # === 步骤7: 确保餐桌区域被包含 ===
    dining_mask[y1:y2, x1:x2] = 1

    # === 步骤8: 最终连通域检查 ===
    _, final_labels = cv2.connectedComponents(dining_mask)
    final_table_label = final_labels[table_center_y, table_center_x]
    if final_table_label > 0:
        dining_mask = (final_labels == final_table_label).astype(np.uint8)

    # === 可视化输出 ===
    vis = cv2.cvtColor(image_gray, cv2.COLOR_GRAY2BGR)
    vis[dining_mask > 0] = [0, 255, 255]  # 黄色为 diningroom
    cv2.rectangle(vis, (x1, y1), (x2, y2), (0, 0, 255), 2)  # 红框为 dining_table_set

    # 绘制圆形候选区域边界
    cv2.circle(vis, (table_center_x, table_center_y), int(dining_radius), (255, 0, 0), 1)

    cv2.imwrite(output_path, vis)
    print(f"[✔] 优化餐厅分割结果已保存至: {output_path}")

    return dining_mask, vis


def extract_diningroom_semantic_segmentation(image_path, table_yolo_label, output_path="diningroom_semantic_result.png"):
    '''
    使用语义分割方法，基于dining_table_set区域进行精确的餐厅区域分割
    重点关注餐桌周围的合理用餐空间，而不是整个连通域
    '''

    # === 读取图像 ===
    image_gray = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    h, w = image_gray.shape

    # === dining_table_set YOLO 格式标签转像素 ===
    _, cx, cy, bw, bh = table_yolo_label
    x1 = int((cx - bw / 2) * w)
    y1 = int((cy - bh / 2) * h)
    x2 = int((cx + bw / 2) * w)
    y2 = int((cy + bh / 2) * h)
    x1, y1, x2, y2 = max(0, x1), max(0, y1), min(w - 1, x2), min(h - 1, y2)
    table_width = x2 - x1
    table_height = y2 - y1

    # === 可通区域提取 ===
    edges = cv2.Canny(image_gray, 50, 150)
    walkable = (image_gray > 200).astype(np.uint8)
    walkable[edges > 0] = 0

    # === 语义分割策略：基于餐桌功能区域 ===
    # 1. 定义餐厅的功能性区域范围
    # 餐厅区域应该包括：餐桌 + 椅子空间 + 通道空间

    # 椅子空间：餐桌周围需要放置椅子的空间
    chair_space = max(table_width, table_height) * 0.4  # 椅子大约是餐桌尺寸的40%

    # 通道空间：人员走动的空间
    passage_space = max(table_width, table_height) * 0.3  # 通道空间

    # 总的餐厅半径
    dining_radius = chair_space + passage_space

    # 2. 创建以餐桌为中心的椭圆形餐厅区域
    table_center_x = (x1 + x2) // 2
    table_center_y = (y1 + y2) // 2

    # 考虑餐桌的长宽比，创建椭圆形区域
    if table_width > table_height:
        # 横向餐桌
        ellipse_a = dining_radius + table_width // 2  # 长轴
        ellipse_b = dining_radius + table_height // 2  # 短轴
    else:
        # 纵向餐桌
        ellipse_a = dining_radius + table_height // 2
        ellipse_b = dining_radius + table_width // 2

    # 3. 创建椭圆形掩码
    y_coords, x_coords = np.ogrid[:h, :w]
    ellipse_mask = ((x_coords - table_center_x) ** 2 / ellipse_a ** 2 +
                    (y_coords - table_center_y) ** 2 / ellipse_b ** 2) <= 1

    # 4. 结合可通行性约束
    dining_candidate = ellipse_mask.astype(np.uint8) & walkable

    # 5. 使用距离变换进行权重分配
    # 创建餐桌掩码
    table_mask = np.zeros_like(walkable)
    table_mask[y1:y2, x1:x2] = 1

    # 计算到餐桌的距离
    distance_to_table = cv2.distanceTransform(1 - table_mask, cv2.DIST_L2, 5)

    # 6. 创建基于距离的权重函数
    # 距离餐桌越近，权重越高
    max_dist = dining_radius
    weight_function = np.exp(-distance_to_table / (max_dist * 0.4))

    # 7. 计算最终的餐厅概率图
    dining_probability = dining_candidate.astype(np.float32) * weight_function

    # 8. 自适应阈值分割
    # 使用Otsu方法自动确定阈值
    prob_uint8 = (dining_probability * 255).astype(np.uint8)
    prob_uint8 = prob_uint8[dining_candidate > 0]  # 只考虑候选区域

    if len(prob_uint8) > 0:
        threshold_val, _ = cv2.threshold(prob_uint8, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        threshold = threshold_val / 255.0
    else:
        threshold = 0.5

    # 应用阈值
    dining_mask = (dining_probability > threshold).astype(np.uint8)

    # 9. 形态学后处理
    kernel = np.ones((3, 3), np.uint8)
    dining_mask = cv2.morphologyEx(dining_mask, cv2.MORPH_CLOSE, kernel)
    dining_mask = cv2.morphologyEx(dining_mask, cv2.MORPH_OPEN, kernel)

    # 10. 确保餐桌区域被包含
    dining_mask[y1:y2, x1:x2] = 1

    # 11. 连通域分析，保留包含餐桌的连通域
    _, labels = cv2.connectedComponents(dining_mask)
    table_label = labels[table_center_y, table_center_x]
    if table_label > 0:
        dining_mask = (labels == table_label).astype(np.uint8)

    # === 可视化输出 ===
    vis = cv2.cvtColor(image_gray, cv2.COLOR_GRAY2BGR)
    vis[dining_mask > 0] = [0, 255, 255]  # 黄色为 diningroom
    cv2.rectangle(vis, (x1, y1), (x2, y2), (0, 0, 255), 2)  # 红框为 dining_table_set

    # 绘制椭圆边界用于调试
    cv2.ellipse(vis, (table_center_x, table_center_y), (int(ellipse_a), int(ellipse_b)), 0, 0, 360, (255, 0, 0), 1)

    cv2.imwrite(output_path, vis)
    print(f"[✔] 语义分割餐厅结果已保存至: {output_path}")

    return dining_mask, vis


def extract_diningroom_from_table_partial(image_path, table_yolo_label, output_path="diningroom_partial_result.png"):
    '''
    基于 dining_table_set 找到 diningroom 区域，但只是连通域的一部分
    使用语义分割的思路，基于餐桌区域进行局部扩展
    '''

    # === 读取图像 ===
    image_gray = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    h, w = image_gray.shape

    # === dining_table_set YOLO 格式标签转像素 ===
    cls, cx, cy, bw, bh = table_yolo_label
    x1 = int((cx - bw / 2) * w)
    y1 = int((cy - bh / 2) * h)
    x2 = int((cx + bw / 2) * w)
    y2 = int((cy + bh / 2) * h)
    x1, y1, x2, y2 = max(0, x1), max(0, y1), min(w - 1, x2), min(h - 1, y2)
    table_width = x2 - x1
    table_height = y2 - y1

    # === 可通区域提取 ===
    edges = cv2.Canny(image_gray, 50, 150)
    walkable = (image_gray > 200).astype(np.uint8)
    walkable[edges > 0] = 0

    # === 基于餐桌区域的语义分割方法 ===
    # 1. 以餐桌为核心，计算合理的餐厅区域范围
    table_center_x = (x1 + x2) // 2
    table_center_y = (y1 + y2) // 2

    # 2. 根据餐桌尺寸确定餐厅区域的合理范围
    # 餐厅区域通常是餐桌周围1.5-2倍的区域
    expansion_factor = 1.8
    dining_width = int(table_width * expansion_factor)
    dining_height = int(table_height * expansion_factor)

    # 3. 计算餐厅区域的边界框
    dining_x1 = max(0, table_center_x - dining_width // 2)
    dining_y1 = max(0, table_center_y - dining_height // 2)
    dining_x2 = min(w, table_center_x + dining_width // 2)
    dining_y2 = min(h, table_center_y + dining_height // 2)

    # 4. 在餐厅区域内进行精细化分割
    dining_region = walkable[dining_y1:dining_y2, dining_x1:dining_x2].copy()

    # 5. 使用距离变换来创建以餐桌为中心的权重图
    table_mask_local = np.zeros_like(dining_region)
    local_x1 = max(0, x1 - dining_x1)
    local_y1 = max(0, y1 - dining_y1)
    local_x2 = min(dining_region.shape[1], x2 - dining_x1)
    local_y2 = min(dining_region.shape[0], y2 - dining_y1)
    table_mask_local[local_y1:local_y2, local_x1:local_x2] = 1

    # 6. 计算从餐桌区域的距离变换
    distance_from_table = cv2.distanceTransform(1 - table_mask_local, cv2.DIST_L2, 5)

    # 7. 创建基于距离的权重，距离餐桌越近权重越高
    max_distance = min(dining_width, dining_height) // 2
    weight_map = np.exp(-distance_from_table / (max_distance * 0.3))

    # 8. 结合可通行性和距离权重
    dining_likelihood = dining_region.astype(np.float32) * weight_map

    # 9. 自适应阈值分割
    threshold = 0.4  # 可调整的阈值
    dining_mask_local = (dining_likelihood > threshold).astype(np.uint8)

    # 10. 形态学处理，去除噪声
    kernel = np.ones((3, 3), np.uint8)
    dining_mask_local = cv2.morphologyEx(dining_mask_local, cv2.MORPH_CLOSE, kernel)
    dining_mask_local = cv2.morphologyEx(dining_mask_local, cv2.MORPH_OPEN, kernel)

    # 11. 确保餐桌区域被包含
    dining_mask_local[local_y1:local_y2, local_x1:local_x2] = 1

    # 12. 连通域分析，保留包含餐桌的最大连通域
    _, labels_local = cv2.connectedComponents(dining_mask_local)
    if labels_local[local_y1 + (local_y2-local_y1)//2, local_x1 + (local_x2-local_x1)//2] > 0:
        target_label = labels_local[local_y1 + (local_y2-local_y1)//2, local_x1 + (local_x2-local_x1)//2]
        dining_mask_local = (labels_local == target_label).astype(np.uint8)

    # 13. 回填到全图
    mask_global = np.zeros_like(walkable)
    mask_global[dining_y1:dining_y2, dining_x1:dining_x2] = dining_mask_local

    # === 可视化输出 ===
    vis = cv2.cvtColor(image_gray, cv2.COLOR_GRAY2BGR)
    vis[mask_global > 0] = [0, 255, 255]  # 黄色为 diningroom
    cv2.rectangle(vis, (x1, y1), (x2, y2), (0, 0, 255), 2)  # 红框为 dining_table_set
    cv2.rectangle(vis, (dining_x1, dining_y1), (dining_x2, dining_y2), (255, 0, 0), 1)  # 蓝框为餐厅候选区域
    cv2.imwrite(output_path, vis)
    print(f"[✔] 部分连通域餐厅分割结果已保存至: {output_path}")

    return mask_global, vis


def extract_diningroom_from_table_exact(image_path, table_yolo_label, output_path="diningroom_exact_result.png"):
    '''
    使用与示例图一致的配置，根据 dining_table_set 自动推理 diningroom（无蓝框先验）
    '''

    # === 读取图像 ===
    image_gray = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    h, w = image_gray.shape

    # === dining_table_set YOLO 格式标签转像素 ===
    cls, cx, cy, bw, bh = table_yolo_label
    x1 = int((cx - bw / 2) * w)
    y1 = int((cy - bh / 2) * h)
    x2 = int((cx + bw / 2) * w)
    y2 = int((cy + bh / 2) * h)
    x1, y1, x2, y2 = max(0, x1), max(0, y1), min(w - 1, x2), min(h - 1, y2)
    table_width = x2 - x1
    table_height = y2 - y1

    # === 可通区域提取 ===
    edges = cv2.Canny(image_gray, 50, 150)
    walkable = (image_gray > 200).astype(np.uint8)
    walkable[edges > 0] = 0

    # === 提取 dining_table_set 内的一个有效像素作为种子点 ===
    sub = walkable[y1:y2, x1:x2]
    coords = np.argwhere(sub == 1)
    if coords.size == 0:
        raise ValueError("未找到有效种子点")
    # 取中位数点（更靠近餐桌中心）
    sy, sx = np.median(coords, axis=0).astype(int)
    seed = (x1 + sx, y1 + sy)

    # === 根据dining_table_set四条边与连通域边界的最小距离确定窗口大小 ===
    def min_distance_to_edge(walkable, x1, y1, x2, y2):
        h, w = walkable.shape

        # 上边界
        dist_top = 0
        for dy in range(1, y1+1):
            region = walkable[y1 - dy, x1:x2]
            if np.any(region == 0):
                dist_top = dy - 1
                break
        else:
            dist_top = y1

        # 下边界
        dist_bottom = 0
        for dy in range(1, h - y2):
            region = walkable[y2 + dy - 1, x1:x2]
            if np.any(region == 0):
                dist_bottom = dy - 1
                break
        else:
            dist_bottom = h - y2 - 1

        # 左边界
        dist_left = 0
        for dx in range(1, x1+1):
            region = walkable[y1:y2, x1 - dx]
            if np.any(region == 0):
                dist_left = dx - 1
                break
        else:
            dist_left = x1

        # 右边界
        dist_right = 0
        for dx in range(1, w - x2):
            region = walkable[y1:y2, x2 + dx - 1]
            if np.any(region == 0):
                dist_right = dx - 1
                break
        else:
            dist_right = w - x2 - 1

        return dist_top, dist_bottom, dist_left, dist_right

    dist_top, dist_bottom, dist_left, dist_right = min_distance_to_edge(walkable, x1, y1, x2, y2)

    # 加点padding，防止太紧贴边界
    pad = 0
    dist_top = max(dist_top - pad, 0)
    dist_bottom = max(dist_bottom - pad, 0)
    dist_left = max(dist_left - pad, 0) 
    dist_right = max(dist_right - pad, 0)

    # if dist_top > dist_bottom:
    #     dist_top = dist_top - 30
    # elif dist_top < dist_bottom:
    #     dist_bottom = dist_bottom - 10


    # 计算窗口大小
    win_w = dist_left + table_width + dist_right
    win_h = dist_top + table_height + dist_bottom

    # 以餐桌中心点为中心，计算窗口范围
    center_x = (x1 + x2) // 2
    center_y = (y1 + y2) // 2

    x_min = max(0, center_x - win_w // 2)
    x_max = min(w, center_x + win_w // 2)
    y_min = max(0, center_y - win_h // 2)
    y_max = min(h, center_y + win_h // 2)

    local = walkable[y_min:y_max, x_min:x_max]
    local_seed = (seed[0] - x_min, seed[1] - y_min)

    # === 连通区域分析 ===
    _, labels_local = cv2.connectedComponents(local)
    target_label = labels_local[local_seed[1], local_seed[0]]
    mask_local = (labels_local == target_label).astype(np.uint8)
    # 填洞 + 小膨胀 + 去小区域
    kernel = np.ones((3, 3), np.uint8)
    mask_local = cv2.morphologyEx(mask_local, cv2.MORPH_CLOSE, kernel)
    mask_local = cv2.morphologyEx(mask_local, cv2.MORPH_OPEN, kernel)

    # # === 回填到整图 ===
    mask_global = np.zeros_like(walkable)
    mask_global[y_min:y_max, x_min:x_max] = mask_local
    # # 在已有 mask_global 后调用：
    # pt1, pt2 = connect_diningtable_top_to_boundary(mask_global, x1, y1, x2)

    mask_global, pt1, pt2 = integrate_top_edge_as_mask(mask_global, x1, y1, x2)
    
    

    

    # === 可视化输出 ===
    vis = cv2.cvtColor(image_gray, cv2.COLOR_GRAY2BGR)
    vis[mask_global > 0] = [0, 255, 255]  # 黄色为 diningroom
    cv2.rectangle(vis, (x1, y1), (x2, y2), (0, 0, 255), 1)  # 红框为 dining_table_set
    # 可视化添加这条边
    cv2.line(vis, pt1, pt2, (255, 0, 0), 1)
    cv2.imwrite(output_path, vis)
    print(f"[✔] 完全一致推理图已保存至: {output_path}")

    return mask_global, vis


# === 示例调用 ===
if __name__ == "__main__":
    # img_path = "/home/<USER>/panpan/code/ultralytics-main/datasets/test1/9176a0d5-shunzao_10499.png"
    # dining_table_label = (5, 0.4201824401368301, 0.7314709236031927, 0.14025085518814137, 0.1630558722919042)

    img_path = "/home/<USER>/panpan/code/ultralytics-main/datasets/test3/7abe895f-shunzao_20277.png"
    dining_table_label = (5, 0.396694214876033,0.36409550045913686,0.07713498622589533,0.10743801652892565)

    # # 测试新的优化方法
    # print("=== 测试优化餐厅分割方法 ===")
    # extract_diningroom_optimized(
    #     image_path=img_path,
    #     table_yolo_label=dining_table_label,
    #     output_path="diningroom_optimized_result.png"
    # )

    # # 测试语义分割方法
    # print("=== 测试语义分割餐厅分割方法 ===")
    # extract_diningroom_semantic_segmentation(
    #     image_path=img_path,
    #     table_yolo_label=dining_table_label,
    #     output_path="diningroom_semantic_result.png"
    # )

    # 测试部分连通域方法
    print("=== 测试部分连通域餐厅分割方法 ===")
    extract_diningroom_from_table_partial(
        image_path=img_path,
        table_yolo_label=dining_table_label,
        output_path="diningroom_partial_result.png"
    )

    # # 原有的完整连通域方法
    # print("=== 测试完整连通域餐厅分割方法 ===")
    # extract_diningroom_from_table_exact(
    #     image_path=img_path,
    #     table_yolo_label=dining_table_label,
    #     output_path="diningroom_exact_result.png"
    # )
