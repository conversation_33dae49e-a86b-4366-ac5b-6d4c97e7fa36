
import cv2
import numpy as np
from skimage.segmentation import find_boundaries

def find_nearest_boundary_point(seed_point, boundary_coords):
    dists = np.linalg.norm(boundary_coords - np.array(seed_point), axis=1)
    idx = np.argmin(dists)
    return tuple(boundary_coords[idx])

def connect_diningtable_top_to_boundary(mask_global, x1, y1, x2):
    # 提取边界点
    boundary_map = find_boundaries(mask_global, mode="outer").astype(np.uint8)
    boundary_coords = np.argwhere(boundary_map > 0)  # [y, x] format

    # 起点：左上角和右上角
    pt1 = (x1, y1)
    pt2 = (x2, y1)

    # 找 mask_global 中与两个点最近的边界点
    nearest1 = find_nearest_boundary_point((y1, x1), boundary_coords)  # 注意: argwhere是(y,x)
    nearest2 = find_nearest_boundary_point((y1, x2), boundary_coords)

    return (nearest1[1], nearest1[0]), (nearest2[1], nearest2[0])  # 转成(x, y)

def integrate_top_edge_as_mask(mask, x1, y1, x2):
    from skimage.segmentation import find_boundaries

    # 找边界
    boundaries = find_boundaries(mask, mode="outer").astype(np.uint8)
    coords = np.argwhere(boundaries > 0)

    def find_nearest(p):
        dists = np.linalg.norm(coords - np.array([p[1], p[0]]), axis=1)
        idx = np.argmin(dists)
        return tuple(coords[idx][::-1])  # 转为(x, y)

    pt1 = find_nearest((x1, y1))
    pt2 = find_nearest((x2, y1))

    # 获取原始 mask 轮廓
    contours, _ = cv2.findContours(mask.copy(), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    if len(contours) == 0:
        raise ValueError("未找到 mask 连通域轮廓")

    contour = contours[0].squeeze()

    # 构建闭合路径：找到离 pt1 和 pt2 最近的 contour 索引
    def find_index(contour, pt):
        dists = np.linalg.norm(contour - np.array(pt), axis=1)
        return np.argmin(dists)

    i1 = find_index(contour, pt1)
    i2 = find_index(contour, pt2)

    # 确保顺序，提取路径
    if i1 < i2:
        path = contour[i1:i2 + 1]
    else:
        path = np.vstack((contour[i1:], contour[:i2 + 1]))

    # 加上 pt2 -> pt1 的直线
    bridge = np.array([pt2, pt1], dtype=np.int32)
    new_contour = np.vstack((path, bridge)).astype(np.int32)

    # 生成新 mask
    new_mask = np.zeros_like(mask)
    cv2.fillPoly(new_mask, [new_contour], 1)

    return new_mask, pt1, pt2

def get_nearest_boundary_polygon(mask, corners):
    boundaries = find_boundaries(mask, mode="outer").astype(np.uint8)
    coords = np.argwhere(boundaries > 0)  # (y, x)

    def nearest_with_fallback(corner):
        dists = np.linalg.norm(coords - np.array([corner[1], corner[0]]), axis=1)
        idx = np.argmin(dists)
        y, x = coords[idx]
        nearest_pt = (x, y)
        # 确保最近点在角点外部或相等，否则用角点
        if (x - corner[0]) ** 2 + (y - corner[1]) ** 2 < 4:  # 太近，不换
            return corner
        else:
            return nearest_pt

    # polygon = [nearest_with_fallback(corner) for corner in corners]

    polygon = []
    for corner in corners:
        nearest_pt = nearest_with_fallback(corner)
        # 强制确保包含 dining_table_set：若最近点在角点“之内”就保留原角点
        dx = nearest_pt[0] - corner[0]
        dy = nearest_pt[1] - corner[1]
        if abs(dx) <= 1 and abs(dy) <= 1:
            polygon.append(corner)  # 最近点太近，保持原始角点
        else:
            # 确保最近点比原点“更外”，否则保留角点
            if (dx >= 0 and dy >= 0) or (dx <= 0 and dy >= 0) or (dx <= 0 and dy <= 0) or (dx >= 0 and dy <= 0):
                polygon.append(nearest_pt)
            else:
                polygon.append(corner)


    # 填充 polygon
    new_mask = np.zeros_like(mask)
    cv2.fillPoly(new_mask, [np.array(polygon, dtype=np.int32)], 1)

    return new_mask, polygon


def extract_diningroom_from_table_exact(image_path, table_yolo_label, output_path="diningroom_exact_result.png"):
    '''
    使用与示例图一致的配置，根据 dining_table_set 自动推理 diningroom（无蓝框先验）
    '''

    # === 读取图像 ===
    image_gray = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    h, w = image_gray.shape

    # === dining_table_set YOLO 格式标签转像素 ===
    cls, cx, cy, bw, bh = table_yolo_label
    x1 = int((cx - bw / 2) * w)
    y1 = int((cy - bh / 2) * h)
    x2 = int((cx + bw / 2) * w)
    y2 = int((cy + bh / 2) * h)
    x1, y1, x2, y2 = max(0, x1), max(0, y1), min(w - 1, x2), min(h - 1, y2)
    table_width = x2 - x1
    table_height = y2 - y1

    # === 可通区域提取 ===
    edges = cv2.Canny(image_gray, 50, 150)
    walkable = (image_gray > 200).astype(np.uint8)
    walkable[edges > 0] = 0

    # === 提取 dining_table_set 内的一个有效像素作为种子点 ===
    sub = walkable[y1:y2, x1:x2]
    coords = np.argwhere(sub == 1)
    if coords.size == 0:
        raise ValueError("未找到有效种子点")
    # 取中位数点（更靠近餐桌中心）
    sy, sx = np.median(coords, axis=0).astype(int)
    seed = (x1 + sx, y1 + sy)

    # === 根据dining_table_set四条边与连通域边界的最小距离确定窗口大小 ===
    def min_distance_to_edge(walkable, x1, y1, x2, y2):
        h, w = walkable.shape

        # 上边界
        dist_top = 0
        for dy in range(1, y1+1):
            region = walkable[y1 - dy, x1:x2]
            if np.any(region == 0):
                dist_top = dy - 1
                break
        else:
            dist_top = y1

        # 下边界
        dist_bottom = 0
        for dy in range(1, h - y2):
            region = walkable[y2 + dy - 1, x1:x2]
            if np.any(region == 0):
                dist_bottom = dy - 1
                break
        else:
            dist_bottom = h - y2 - 1

        # 左边界
        dist_left = 0
        for dx in range(1, x1+1):
            region = walkable[y1:y2, x1 - dx]
            if np.any(region == 0):
                dist_left = dx - 1
                break
        else:
            dist_left = x1

        # 右边界
        dist_right = 0
        for dx in range(1, w - x2):
            region = walkable[y1:y2, x2 + dx - 1]
            if np.any(region == 0):
                dist_right = dx - 1
                break
        else:
            dist_right = w - x2 - 1

        return dist_top, dist_bottom, dist_left, dist_right

    dist_top, dist_bottom, dist_left, dist_right = min_distance_to_edge(walkable, x1, y1, x2, y2)

    # 加点padding，防止太紧贴边界
    pad = 0
    dist_top = max(dist_top - pad, 0)
    dist_bottom = max(dist_bottom - pad, 0)
    dist_left = max(dist_left - pad, 0) 
    dist_right = max(dist_right - pad, 0)

    # if dist_top > dist_bottom:
    #     dist_top = dist_top - 30
    # elif dist_top < dist_bottom:
    #     dist_bottom = dist_bottom - 10


    # 计算窗口大小
    win_w = dist_left + table_width + dist_right
    win_h = dist_top + table_height + dist_bottom

    # 以餐桌中心点为中心，计算窗口范围
    center_x = (x1 + x2) // 2
    center_y = (y1 + y2) // 2

    x_min = max(0, center_x - win_w // 2)
    x_max = min(w, center_x + win_w // 2)
    y_min = max(0, center_y - win_h // 2)
    y_max = min(h, center_y + win_h // 2)

    local = walkable[y_min:y_max, x_min:x_max]
    local_seed = (seed[0] - x_min, seed[1] - y_min)

    # === 连通区域分析 ===
    _, labels_local = cv2.connectedComponents(local)
    target_label = labels_local[local_seed[1], local_seed[0]]
    mask_local = (labels_local == target_label).astype(np.uint8)
    # 填洞 + 小膨胀 + 去小区域
    kernel = np.ones((3, 3), np.uint8)
    mask_local = cv2.morphologyEx(mask_local, cv2.MORPH_CLOSE, kernel)
    mask_local = cv2.morphologyEx(mask_local, cv2.MORPH_OPEN, kernel)

    # # === 回填到整图 ===
    mask_global = np.zeros_like(walkable)
    mask_global[y_min:y_max, x_min:x_max] = mask_local
    # # 在已有 mask_global 后调用：
    # pt1, pt2 = connect_diningtable_top_to_boundary(mask_global, x1, y1, x2)

    mask_global, pt1, pt2 = integrate_top_edge_as_mask(mask_global, x1, y1, x2)
    
    

    

    # === 可视化输出 ===
    vis = cv2.cvtColor(image_gray, cv2.COLOR_GRAY2BGR)
    vis[mask_global > 0] = [0, 255, 255]  # 黄色为 diningroom
    cv2.rectangle(vis, (x1, y1), (x2, y2), (0, 0, 255), 1)  # 红框为 dining_table_set
    # 可视化添加这条边
    cv2.line(vis, pt1, pt2, (255, 0, 0), 1)
    cv2.imwrite(output_path, vis)
    print(f"[✔] 完全一致推理图已保存至: {output_path}")

    return mask_global, vis


# === 示例调用 ===
if __name__ == "__main__":
    # img_path = "/home/<USER>/panpan/code/ultralytics-main/datasets/test1/9176a0d5-shunzao_10499.png"
    # dining_table_label = (5, 0.4201824401368301, 0.7314709236031927, 0.14025085518814137, 0.1630558722919042)

    img_path = "/home/<USER>/panpan/code/ultralytics-main/datasets/test3/7abe895f-shunzao_20277.png"
    dining_table_label = (5, 0.396694214876033,0.36409550045913686,0.07713498622589533,0.10743801652892565)
    extract_diningroom_from_table_exact(
        image_path=img_path,
        table_yolo_label=dining_table_label,
        output_path="diningroom_from_table.png"
    )
