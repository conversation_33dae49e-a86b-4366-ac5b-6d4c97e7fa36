import cv2
import numpy as np
import os
from pathlib import Path
from scipy.ndimage import binary_fill_holes, binary_dilation
from skimage.segmentation import find_boundaries

class RoomSegmenter:
    def __init__(self,
                 image_dir,
                 label_dir,
                 output_dir="output",
                 area_thresh=100):

        self.image_dir = image_dir
        self.label_dir = label_dir
        self.output_dir = output_dir
        self.area_thresh = area_thresh

        self.class_names = {
            0: "bed_grounded",
            1: "bed_highleg",
            2: "sofa_grounded",
            3: "sofa_highleg",
            4: "door",
            5: "dining_table_set"
        }

        self.room_type_map = {
            0: "bedroom",
            1: "bedroom",
            2: "livingroom",
            3: "livingroom",
            5: "diningroom",
            6: "kitchen"
        }

        self.room_color_map = {
            "bedroom": (128, 128, 255),
            "livingroom": (128, 255, 128),
            "diningroom": (255, 128, 128),
            "kitchen": (0, 255, 255),
            "unknow": (255, 192, 0)
        }

        self.object_color_map = {
            "bed_grounded": (160, 160, 255),
            "bed_highleg": (100, 100, 255),
            "sofa_grounded": (160, 255, 160),
            "sofa_highleg": (100, 255, 100),
            "door": (255, 0, 255),
            "dining_table_set": (0, 128, 255)
        }

        self.font = cv2.FONT_HERSHEY_SIMPLEX

    def load_yolo_labels(self, label_file, img_w, img_h):
        boxes, classes = [], []
        with open(label_file, 'r') as f:
            for line in f:
                cls, cx, cy, w, h = map(float, line.strip().split())
                cls = int(cls)
                x1 = int((cx - w / 2) * img_w)
                y1 = int((cy - h / 2) * img_h)
                x2 = int((cx + w / 2) * img_w)
                y2 = int((cy + h / 2) * img_h)
                boxes.append([x1, y1, x2, y2])
                classes.append(cls)
        return boxes, classes
    
    
    def ray_to_boundary_intersection(self, start_x, start_y, dx, dy, boundary_mask):
        """
        从 (start_x, start_y) 向 dx, dy 方向发射，直到遇到边界像素（boundary_mask为True）
        """
        h, w = boundary_mask.shape
        x, y = start_x, start_y
        while 0 <= x < w and 0 <= y < h:
            if boundary_mask[int(y), int(x)]:
                return int(x), int(y)
            x += dx
            y += dy
        return None

    


    def bridge_merge_labels(self, labels_im, room_type_for_label, kernel_size=5, iterations=3, overlap_thresh=20):
        """
        膨胀同类房间的连通域掩码，若膨胀后有重叠则合并，减少被门分割导致的断裂。

        labels_im: int label image
        room_type_for_label: dict {label: room_type string}
        kernel_size: 膨胀核大小
        iterations: 膨胀次数
        overlap_thresh: 重叠像素阈值，超过则合并

        返回合并后的labels_im和更新的room_type_for_label
        """
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))
        merged_labels = labels_im.copy()

        # 获取所有标签（排除0背景）
        labels = [l for l in np.unique(labels_im) if l != 0]

        # 记录掩码和膨胀掩码
        masks = {l: (merged_labels == l) for l in labels}
        dilated_masks = {l: cv2.dilate(masks[l].astype(np.uint8), kernel, iterations=iterations).astype(bool) for l in labels}

        # 合并映射，初始自己指向自己
        parent = {l: l for l in labels}

        def find(x):
            while parent[x] != x:
                parent[x] = parent[parent[x]]
                x = parent[x]
            return x

        def union(a, b):
            pa, pb = find(a), find(b)
            if pa != pb:
                parent[pb] = pa

        # 两两比较重叠且同类房间的连通域合并
        for i, l1 in enumerate(labels):
            for l2 in labels[i+1:]:
                if room_type_for_label.get(l1) != room_type_for_label.get(l2):
                    continue
                overlap = np.sum(dilated_masks[l1] & masks[l2])
                if overlap > overlap_thresh:
                    union(l1, l2)

        # 重新标记连通域
        label_map = {}
        new_label = 1
        new_room_type_for_label = {}
        new_labels_im = np.zeros_like(labels_im)

        for l in labels:
            root = find(l)
            if root not in label_map:
                label_map[root] = new_label
                new_room_type_for_label[new_label] = room_type_for_label.get(root, "unknow")
                new_label += 1
            label_map[l] = label_map[root]

        for l in labels:
            new_labels_im[merged_labels == l] = label_map[l]

        return new_labels_im, new_room_type_for_label


    def merge_regions(self, labels_im, num_labels):
        # 构建每个连通区域的掩码和面积信息
        region_masks = {}
        region_areas = {}

        for i in range(1, num_labels):
            mask = (labels_im == i)
            area = np.sum(mask)
            region_masks[i] = mask
            region_areas[i] = area

        # 从大区域向小区域尝试合并
        merged_labels = labels_im.copy()
        label_list = sorted(region_areas.items(), key=lambda x: x[1], reverse=True)  # 按面积从大到小排序

        for big_label, big_area in label_list:
            big_mask = region_masks[big_label]
            big_mask_dilated = binary_dilation(big_mask, iterations=5)  # 向外膨胀一圈，避免紧贴边界

            for small_label, small_area in label_list:
                if small_label == big_label or small_area == 0:
                    continue
                small_mask = region_masks[small_label]

                # 如果小区域大部分像素都在大区域膨胀范围内，则归并
                overlap = big_mask_dilated & small_mask
                if np.sum(overlap) > 0.2 * np.sum(small_mask):  # 可调阈值：80%
                    merged_labels[merged_labels == small_label] = big_label  # 合并
                    region_masks[big_label] = (merged_labels == big_label)  # 更新大区域mask
                    region_areas[big_label] += region_areas[small_label]
                    region_areas[small_label] = 0  # 被合并掉


        return merged_labels, region_areas

    def classify_and_visualize(self, image, labels_im, boxes, classes, region_areas, save_dir, img_name):
        h, w = labels_im.shape
        vis_image = image.copy()
        color_mask = np.zeros_like(image)
        room_idx = 0
        room_type_for_label = {}

        # 找最大连通域label
        max_region_label = max(region_areas, key=region_areas.get)
        max_region_area = region_areas[max_region_label]

        for i in range(1, np.max(labels_im)+1):
            if region_areas.get(i, 0) < self.area_thresh:
                continue
            mask = (labels_im == i)
            present_classes = set()
            for (x1, y1, x2, y2), cls in zip(boxes, classes):
                if cls == 4:
                    continue
                cx = (x1 + x2) // 2
                cy = (y1 + y2) // 2
                win = 1
                in_region = False
                for dx in range(-win, win + 1):
                    for dy in range(-win, win + 1):
                        nx, ny = cx + dx, cy + dy
                        if 0 <= nx < w and 0 <= ny < h and mask[ny, nx]:
                            in_region = True
                            break
                    if in_region:
                        present_classes.add(cls)

            if 5 in present_classes and (2 in present_classes or 3 in present_classes):
                room_type = "livingroom"
            else:
                room_type = "unknow"
                for cls in present_classes:
                    if cls in self.room_type_map:
                        room_type = self.room_type_map[cls]
                        break

            if i == max_region_label and room_type == "diningroom" and max_region_area > 5000:
                room_type = "livingroom"

            
            mask_filled = binary_fill_holes(mask)
            mask_uint8 = (mask_filled * 255).astype(np.uint8)
            contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            filled_mask = np.zeros_like(mask_uint8)
            cv2.drawContours(filled_mask, contours, -1, 255, thickness=-1)
            filled_mask_bool = filled_mask.astype(bool)

            color = self.room_color_map[room_type]
            vis_image[filled_mask_bool] = cv2.addWeighted(vis_image, 0.5, np.full_like(image, color), 0.7, 0)[filled_mask_bool]
            color_mask[filled_mask_bool] = color

            ys, xs = np.where(filled_mask_bool)
            if len(xs) > 0 and len(ys) > 0:
                cx, cy = int(np.mean(xs)), int(np.mean(ys))
                cv2.putText(vis_image, room_type, (cx - 40, cy), self.font, 0.5, (0, 0, 0), 2)

            # # 保存mask
            cv2.imwrite(f"{save_dir}/{room_type}_{room_idx}.png", (mask_filled.astype(np.uint8) * 255))
            room_idx += 1

        # 绘制门框
        for (x1, y1, x2, y2), cls in zip(boxes, classes):
            if cls == 4:
                cv2.rectangle(vis_image, (x1, y1), (x2, y2), (255, 0, 255), 2)

        # 绘制目标框和中心点
        boxed_image = image.copy()
        for (x1, y1, x2, y2), cls in zip(boxes, classes):
            label = self.class_names.get(cls, str(cls))
            color = self.object_color_map.get(label, (0, 255, 255))
            cv2.rectangle(boxed_image, (x1, y1), (x2, y2), color, 2)
            if label != "door":
                cx = (x1 + x2) // 2
                cy = (y1 + y2) // 2
                cv2.circle(boxed_image, (cx, cy), 3, (0, 0, 255), -1)
        # 新增：基于类别合并连通域，减少断裂
        labels_im, room_type_for_label = self.bridge_merge_labels(labels_im, room_type_for_label)

        # 重新计算区域面积等（可复用merge_regions逻辑）
        _, region_areas = self.merge_regions(labels_im, np.max(labels_im) + 1)
        
        # 拼接结果图
        vis_h, vis_w = vis_image.shape[:2]
        legend_height = 100
        final_image = np.ones((vis_h + legend_height, vis_w * 2, 3), dtype=np.uint8) * 255
        final_image[legend_height:, :vis_w] = vis_image
        final_image[legend_height:, vis_w:] = boxed_image
        cv2.line(final_image, (vis_w, legend_height), (vis_w, vis_h + legend_height), (255, 255, 255), 2)

        # 添加图例
        self._draw_legend(final_image, legend_height)

        # 保存结果
        cv2.imwrite(f"{save_dir}/overlay.png", vis_image)
        cv2.imwrite(f"{save_dir}/mask_color.png", color_mask)
        cv2.imwrite(f"{save_dir}/boxed.png", boxed_image)
        cv2.imwrite(f"{save_dir}/{img_name}_final_result.png", final_image)

    def _draw_legend(self, final_image, legend_height):
        legend_font = 0.45
        y_offset_2 = 20
        y_offset_3 = 50
        y_offset_4 = 80
        x_step = 150

        for i, label in enumerate(["bed_grounded", "bed_highleg"]):
            color = self.object_color_map[label]
            x = 10 + i * x_step
            cv2.rectangle(final_image, (x, y_offset_2 - 15), (x + 20, y_offset_2 + 5), color, -1)
            cv2.putText(final_image, label, (x + 25, y_offset_2 + 2), self.font, legend_font, (0, 0, 0), 1)

        for i, label in enumerate(["sofa_grounded", "sofa_highleg"]):
            color = self.object_color_map[label]
            x = 10 + i * x_step
            cv2.rectangle(final_image, (x, y_offset_3 - 15), (x + 20, y_offset_3 + 5), color, -1)
            cv2.putText(final_image, label, (x + 25, y_offset_3 + 2), self.font, legend_font, (0, 0, 0), 1)

        for i, label in enumerate(["door", "dining_table_set"]):
            color = self.object_color_map[label]
            x = 10 + i * x_step
            cv2.rectangle(final_image, (x, y_offset_4 - 15), (x + 20, y_offset_4 + 5), color, -1)
            cv2.putText(final_image, label, (x + 25, y_offset_4 + 2), self.font, legend_font, (0, 0, 0), 1)

    def process_image(self, image_path, label_path):
        img_name = Path(image_path).stem
        image = cv2.imread(image_path)
        if image is None:
            print(f"⚠️ Failed to load image: {image_path}")
            return
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        h, w = gray.shape

        boxes, classes = self.load_yolo_labels(label_path, w, h)

        _, binary = cv2.threshold(gray, 240, 255, cv2.THRESH_BINARY)

        for (x1, y1, x2, y2), cls in zip(boxes, classes):
            if cls == 4:  # door
                cv2.rectangle(binary, (x1, y1), (x2, y2 + 1), 0, -1)
            elif cls in [0, 1, 2, 3, 5]:
                cv2.rectangle(binary, (x1, y1), (x2, y2), 255, -1)

        num_labels, labels_im = cv2.connectedComponents(binary)
        merged_labels, region_areas = self.merge_regions(labels_im, num_labels)

        save_dir = os.path.join(self.output_dir, img_name)
        os.makedirs(save_dir, exist_ok=True)
        self.classify_and_visualize(image, merged_labels, boxes, classes, region_areas, save_dir, img_name)
        print(f"✅ Processed {img_name}, Saved to {save_dir}")

    def batch_process(self):
        os.makedirs(self.output_dir, exist_ok=True)
        for img_file in sorted(os.listdir(self.image_dir)):
            if not img_file.lower().endswith((".jpg", ".png")):
                continue
            img_path = os.path.join(self.image_dir, img_file)
            label_path = os.path.join(self.label_dir, Path(img_file).with_suffix(".txt"))
            if not os.path.exists(label_path):
                print(f"⚠️ Label not found for {img_file}")
                continue
            self.process_image(img_path, label_path)


if __name__ == "__main__":
    IMAGE_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/salm_0617/images/val"
    LABEL_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/salm_0617/labels/val"

    IMAGE_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/test"
    LABEL_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/test"
    OUTPUT_DIR = "output"
    AREA_THRESH = 100

    segmenter = RoomSegmenter(IMAGE_DIR, LABEL_DIR, OUTPUT_DIR, AREA_THRESH)
    segmenter.batch_process()



# import cv2
# import numpy as np
# import os
# from pathlib import Path
# from scipy.ndimage import binary_fill_holes, binary_dilation
# from skimage.segmentation import find_boundaries
# from sklearn.cluster import KMeans
# from scipy.ndimage import binary_erosion, binary_dilation, binary_fill_holes
# class RoomSegmenter:
#     def __init__(self,
#                  image_dir,
#                  label_dir,
#                  output_dir="output",
#                  area_thresh=100):

#         self.image_dir = image_dir
#         self.label_dir = label_dir
#         self.output_dir = output_dir
#         self.area_thresh = area_thresh

#         self.class_names = {
#             0: "bed_grounded",
#             1: "bed_highleg",
#             2: "sofa_grounded",
#             3: "sofa_highleg",
#             4: "door",
#             5: "dining_table_set"
#         }

#         self.room_type_map = {
#             0: "bedroom",
#             1: "bedroom",
#             2: "livingroom",
#             3: "livingroom",
#             5: "diningroom",
#             6: "kitchen"
#         }

#         self.room_color_map = {
#             "bedroom": (128, 128, 255),
#             "livingroom": (128, 255, 128),
#             "diningroom": (255, 128, 128),
#             "kitchen": (0, 255, 255),
#             "unknow": (255, 192, 0)
#         }

#         self.object_color_map = {
#             "bed_grounded": (160, 160, 255),
#             "bed_highleg": (100, 100, 255),
#             "sofa_grounded": (160, 255, 160),
#             "sofa_highleg": (100, 255, 100),
#             "door": (255, 0, 255),
#             "dining_table_set": (0, 128, 255)
#         }

#         self.font = cv2.FONT_HERSHEY_SIMPLEX

#     def load_yolo_labels(self, label_file, img_w, img_h):
#         boxes, classes = [], []
#         with open(label_file, 'r') as f:
#             for line in f:
#                 parts = line.strip().split()
#                 if not parts:
#                     continue
#                 try:
#                     cls = int(float(parts[0]))
#                     cx = float(parts[1])
#                     cy = float(parts[2])
#                     w = float(parts[3])
#                     h = float(parts[4])
#                     x1 = int((cx - w / 2) * img_w)
#                     y1 = int((cy - h / 2) * img_h)
#                     x2 = int((cx + w / 2) * img_w)
#                     y2 = int((cy + h / 2) * img_h)
#                     boxes.append([x1, y1, x2, y2])
#                     classes.append(cls)
#                 except (ValueError, IndexError) as e:
#                     print(f"Error parsing line: {line.strip()} - {e}")
#         return boxes, classes

    
#     def refine_dining_area(self, dining_mask, region_mask, boxes, classes):
#         """优化过大的餐厅区域"""
#         # 1. 检测区域内其他家具
#         other_furniture = np.zeros_like(dining_mask)
#         for (x1, y1, x2, y2), cls in zip(boxes, classes):
#             if cls != 5:  # 非餐桌家具
#                 cv2.rectangle(other_furniture, (x1, y1), (x2, y2), 1, -1)
        
#         # 2. 计算家具密度图
#         kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (15, 15))
#         density_map = cv2.filter2D(other_furniture.astype(np.float32), -1, kernel)
        
#         # 3. 在餐厅区域内找到家具密集区
#         high_density = (density_map > 0.2) & dining_mask
        
#         # 4. 从餐厅区域中排除高密度非餐桌区域
#         refined_mask = dining_mask & ~high_density
        
#         # 5. 确保区域连通性
#         if np.any(refined_mask):
#             num_labels, labels = cv2.connectedComponents(refined_mask.astype(np.uint8))
#             if num_labels > 1:
#                 largest_label = np.argmax([np.sum(labels == i) for i in range(1, num_labels)]) + 1
#                 refined_mask = (labels == largest_label)
        
#         return refined_mask if np.any(refined_mask) else dining_mask
    
#     def bridge_merge_labels(self, labels_im, room_type_for_label, kernel_size=5, iterations=3, overlap_thresh=20):
#         kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))
#         merged_labels = labels_im.copy()

#         labels = [l for l in np.unique(labels_im) if l != 0]
#         masks = {l: (merged_labels == l) for l in labels}
#         dilated_masks = {l: cv2.dilate(masks[l].astype(np.uint8), kernel, iterations=iterations).astype(bool) for l in labels}

#         parent = {l: l for l in labels}

#         def find(x):
#             while parent[x] != x:
#                 parent[x] = parent[parent[x]]
#                 x = parent[x]
#             return x

#         def union(a, b):
#             pa, pb = find(a), find(b)
#             if pa != pb:
#                 parent[pb] = pa

#         for i, l1 in enumerate(labels):
#             for l2 in labels[i+1:]:
#                 if room_type_for_label.get(l1) != room_type_for_label.get(l2):
#                     continue
#                 overlap = np.sum(dilated_masks[l1] & masks[l2])
#                 if overlap > overlap_thresh:
#                     union(l1, l2)

#         label_map = {}
#         new_label = 1
#         new_room_type_for_label = {}
#         new_labels_im = np.zeros_like(labels_im)

#         for l in labels:
#             root = find(l)
#             if root not in label_map:
#                 label_map[root] = new_label
#                 new_room_type_for_label[new_label] = room_type_for_label.get(root, "unknow")
#                 new_label += 1
#             label_map[l] = label_map[root]

#         for l in labels:
#             new_labels_im[merged_labels == l] = label_map[l]

#         return new_labels_im, new_room_type_for_label

#     def get_precise_dining_area(self, region_mask, boxes, classes, walls_mask):
#         """
#         生成精确的餐厅语义分割区域
#         :param region_mask: 当前连通域掩码(bool或uint8)
#         :param boxes: 所有检测框
#         :param classes: 所有类别
#         :param walls_mask: 墙体掩码(bool或uint8)
#         :return: 餐厅区域精确掩码(bool)
#         """
#         # 确保输入掩码是uint8类型
#         region_mask = region_mask.astype(np.uint8) if region_mask.dtype == bool else region_mask
#         walls_mask = walls_mask.astype(np.uint8) if walls_mask.dtype == bool else walls_mask
        
#         # 1. 提取所有餐桌位置
#         dining_tables = []
#         for (x1, y1, x2, y2), cls in zip(boxes, classes):
#             if cls == 5:  # dining_table_set
#                 center = ((x1 + x2) // 2, (y1 + y2) // 2)
#                 dining_tables.append(center)

#         if not dining_tables:
#             return None

#         # 2. 创建初始区域(使用uint8类型)
#         dining_mask = np.zeros_like(region_mask, dtype=np.uint8)
#         for cx, cy in dining_tables:
#             if 0 <= cx < dining_mask.shape[1] and 0 <= cy < dining_mask.shape[0]:
#                 dining_mask[cy, cx] = 255

#         # 3. 区域生长算法
#         kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
#         for _ in range(100):  # 最大迭代次数
#             new_mask = cv2.dilate(dining_mask, kernel)
#             new_mask = cv2.bitwise_and(new_mask, region_mask)  # 限制在原始连通域内
#             new_mask = cv2.bitwise_and(new_mask, cv2.bitwise_not(walls_mask))  # 不能穿过墙体
            
#             if np.array_equal(new_mask, dining_mask):  # 不再变化
#                 break
#             dining_mask = new_mask

#         # 4. 后处理优化
#         if np.any(dining_mask):
#             # 转换为bool类型进行处理
#             dining_mask_bool = dining_mask.astype(bool)
            
#             # 4.1 填充小孔洞
#             dining_mask_bool = binary_fill_holes(dining_mask_bool)
            
#             # 4.2 只保留最大连通区域
#             num_labels, labels = cv2.connectedComponents(dining_mask.astype(np.uint8))
#             if num_labels > 1:
#                 largest_label = np.argmax([np.sum(labels == i) for i in range(1, num_labels)]) + 1
#                 dining_mask_bool = (labels == largest_label)
            
#             # 4.3 平滑边界
#             dining_mask_bool = binary_dilation(binary_erosion(dining_mask_bool))
#             return dining_mask_bool
        
#         return None

#     def detect_walls(self, gray_image):
#         """
#         改进的墙体检测方法
#         :return: 墙体掩码(True表示墙体)
#         """
#         # 1. 边缘检测
#         edges = cv2.Canny(gray_image, 50, 150)
        
#         # 2. 线段检测
#         lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=50, 
#                             minLineLength=50, maxLineGap=10)
        
#         # 3. 生成墙体掩码(使用uint8类型)
#         walls_mask = np.zeros_like(gray_image, dtype=np.uint8)
#         if lines is not None:
#             for line in lines:
#                 x1, y1, x2, y2 = line[0]
#                 cv2.line(walls_mask, (x1, y1), (x2, y2), color=255, thickness=2)  # 使用255表示墙体
        
#         # 4. 形态学处理并转换回bool类型
#         kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
#         walls_mask = cv2.dilate(walls_mask, kernel)
#         walls_mask = walls_mask.astype(bool)  # 转换为布尔类型
        
#         return walls_mask
#     def merge_regions(self, labels_im, num_labels, boxes=None, classes=None):
#         region_masks = {}
#         region_areas = {}
#         protected_labels = set()

#         # 识别受保护区域（包含dining_table_set）
#         if boxes is not None and classes is not None:
#             h, w = labels_im.shape
#             for (x1, y1, x2, y2), cls in zip(boxes, classes):
#                 if cls == 5:  # dining_table_set
#                     cx = (x1 + x2) // 2
#                     cy = (y1 + y2) // 2
#                     if 0 <= cx < w and 0 <= cy < h:
#                         label_val = labels_im[cy, cx]
#                         if label_val > 0:
#                             protected_labels.add(label_val)

#         for i in range(1, num_labels):
#             mask = (labels_im == i)
#             area = np.sum(mask)
#             region_masks[i] = mask
#             region_areas[i] = area

#         merged_labels = labels_im.copy()
#         label_list = sorted(region_areas.items(), key=lambda x: x[1], reverse=True)

#         for big_label, big_area in label_list:
#             if big_label not in region_masks or region_areas[big_label] == 0:
#                 continue
                
#             big_mask = region_masks[big_label]
#             big_mask_dilated = binary_dilation(big_mask, iterations=5)

#             for small_label, small_area in label_list:
#                 if (small_label == big_label or region_areas[small_label] == 0 or 
#                     small_label in protected_labels):
#                     continue
                    
#                 small_mask = region_masks[small_label]
#                 overlap = big_mask_dilated & small_mask
                
#                 if np.sum(overlap) > 0.2 * np.sum(small_mask):
#                     merged_labels[merged_labels == small_label] = big_label
#                     region_masks[big_label] = (merged_labels == big_label)
#                     region_areas[big_label] += region_areas[small_label]
#                     region_areas[small_label] = 0

#         return merged_labels, region_areas

#     def segment_dining_subregions(self, region_mask, boxes, classes, img_w, img_h):
#         """优化后的餐厅子区域识别"""
#         # 1. 查找所有餐桌位置和尺寸
#         dining_tables = []
#         for (x1, y1, x2, y2), cls in zip(boxes, classes):
#             if cls == 5:  # dining_table_set
#                 cx = (x1 + x2) // 2
#                 cy = (y1 + y2) // 2
#                 table_size = max(x2-x1, y2-y1)  # 取长宽较大值作为尺寸参考
#                 dining_tables.append((cx, cy, table_size))
        
#         if not dining_tables:
#             return None
        
#         # 2. 创建加权距离图（考虑餐桌尺寸）
#         distance_map = np.full(region_mask.shape, np.inf, dtype=np.float32)
#         for y in range(region_mask.shape[0]):
#             for x in range(region_mask.shape[1]):
#                 if region_mask[y, x]:
#                     min_dist = np.inf
#                     for tx, ty, size in dining_tables:
#                         dist = np.sqrt((x - tx)**2 + (y - ty)**2)
#                         # 加权距离：实际距离/餐桌尺寸，使大餐桌影响范围更大
#                         weighted_dist = dist / (size * 0.5)  
#                         if weighted_dist < min_dist:
#                             min_dist = weighted_dist
#                     distance_map[y, x] = min_dist
        
#         # 3. 动态确定餐厅区域半径（基于餐桌尺寸和密度）
#         avg_table_size = np.mean([size for _, _, size in dining_tables])
#         max_radius = min(img_w, img_h) * 0.1  # 最大不超过图像尺寸的20%
#         base_radius = avg_table_size * 1.5  # 基础半径
        
#         # 4. 带衰减的距离变换
#         dining_likelihood = np.zeros_like(distance_map)
#         dining_likelihood[region_mask] = np.exp(-distance_map[region_mask] * 1)  # 衰减系数
        
#         # 5. 自适应阈值分割
#         threshold = 0.5 * (1 - np.exp(-base_radius/max_radius))
#         dining_mask = (dining_likelihood > threshold) & region_mask
        
#         # 6. 后处理：确保区域连通性
#         if np.any(dining_mask):
#             # 只保留最大的连通区域
#             num_labels, labels = cv2.connectedComponents(dining_mask.astype(np.uint8))
#             if num_labels > 1:
#                 largest_label = np.argmax([np.sum(labels == i) for i in range(1, num_labels)]) + 1
#                 dining_mask = (labels == largest_label)
        
#         return dining_mask if np.any(dining_mask) else None

#     def classify_and_visualize(self, image, labels_im, boxes, classes, region_areas, save_dir, img_name):
#         h, w = labels_im.shape
#         vis_image = image.copy()
#         color_mask = np.zeros_like(image)
#         room_idx = 0
#         room_type_for_label = {}

#         # 第一轮：识别所有dining_table_set所在的区域
#         dining_areas = {}
#         for (x1, y1, x2, y2), cls in zip(boxes, classes):
#             if cls == 5:  # dining_table_set
#                 cx = (x1 + x2) // 2
#                 cy = (y1 + y2) // 2
#                 if 0 <= cx < w and 0 <= cy < h:
#                     label_val = labels_im[cy, cx]
#                     if label_val > 0:
#                         if label_val not in dining_areas:
#                             dining_areas[label_val] = []
#                         dining_areas[label_val].append((cx, cy))

#         # 第二轮：分类区域并识别餐厅子区域
#         for i in range(1, np.max(labels_im) + 1):
#             if region_areas.get(i, 0) < self.area_thresh:
#                 continue
                
#             mask = (labels_im == i)
#             present_classes = set()
            
#             # 检查区域内包含的物体
#             for (x1, y1, x2, y2), cls in zip(boxes, classes):
#                 if cls == 4:  # 跳过门
#                     continue
                    
#                 cx = (x1 + x2) // 2
#                 cy = (y1 + y2) // 2
#                 win = 1
#                 in_region = False
                
#                 for dx in range(-win, win + 1):
#                     for dy in range(-win, win + 1):
#                         nx, ny = cx + dx, cy + dy
#                         if 0 <= nx < w and 0 <= ny < h and mask[ny, nx]:
#                             in_region = True
#                             break
#                     if in_region:
#                         break
                        
#                 if in_region:
#                     present_classes.add(cls)

#             # 优先识别餐厅区域
#             room_type = "unknow"
#             for cls in present_classes:
#                 if cls in self.room_type_map:
#                     room_type = self.room_type_map[cls]
#                     break
            
#             # 检测墙体(新增)
#             gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
#             walls_mask = self.detect_walls(gray)
            
#             # 优化后的餐厅区域处理
#             if i in dining_areas:
#                 # 修改后调用示例
#                 dining_subregion = self.get_precise_dining_area(
#                     mask.astype(np.uint8),  # 确保输入是uint8
#                     boxes, 
#                     classes, 
#                     walls_mask.astype(np.uint8)  # 确保输入是uint8
#                 )

#                 if dining_subregion is not None:
#                     dining_subregion = dining_subregion.astype(np.uint8)  # 可视化前确保类型
#                     # # 计算餐厅区域合理范围
#                     # dining_area = np.sum(dining_subregion)
#                     # total_area = np.sum(mask)
                    
#                     # # 区域可视化
#                     # dining_color = self.room_color_map["diningroom"]
#                     # vis_image[dining_subregion] = cv2.addWeighted(
#                     #     vis_image[dining_subregion], 0.5,
#                     #     np.full_like(vis_image, dining_color), 0.7, 0
#                     # )
#                     color_img = np.zeros_like(image, dtype=np.uint8)
#                     color_img[:] = color_img
#                     blended = cv2.addWeighted(vis_image, 0.5, color_img, 0.7, 0)
#                     # vis_image[filled_mask_bool] = blended[filled_mask_bool]
#                 # 计算子区域中心并添加标签
#                 ys, xs = np.where(dining_subregion)
#                 if len(xs) > 0 and len(ys) > 0:
#                     cx, cy = int(np.mean(xs)), int(np.mean(ys))
#                     cv2.putText(vis_image, "diningroom", (cx - 40, cy), self.font, 0.5, (0, 0, 0), 2)
                
#                 room_idx += 1
                
#                 # 将剩余区域标记为原始房间类型
#                 remaining_mask = mask & ~dining_subregion
#                 if np.any(remaining_mask):
#                     # 填充和可视化剩余区域
#                     mask_filled = binary_fill_holes(remaining_mask)
#                     mask_uint8 = (mask_filled * 255).astype(np.uint8)
#                     contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
#                     filled_mask = np.zeros_like(mask_uint8)
#                     cv2.drawContours(filled_mask, contours, -1, 255, thickness=-1)
#                     filled_mask_bool = filled_mask.astype(bool)

#                     # color = self.room_color_map[room_type]
#                     # vis_image[filled_mask_bool] = cv2.addWeighted(
#                     #     vis_image[filled_mask_bool], 0.5, 
#                     #     np.full_like(image, color), 0.7, 0
#                     # )[filled_mask_bool]
#                     color = self.room_color_map[room_type]
#                     color_img = np.zeros_like(image, dtype=np.uint8)
#                     color_img[:] = color
#                     blended = cv2.addWeighted(vis_image, 0.5, color_img, 0.7, 0)
#                     vis_image[filled_mask_bool] = blended[filled_mask_bool]

#                     color_mask[filled_mask_bool] = color

#                     ys, xs = np.where(filled_mask_bool)
#                     if len(xs) > 0 and len(ys) > 0:
#                         cx, cy = int(np.mean(xs)), int(np.mean(ys))
#                         cv2.putText(vis_image, room_type, (cx - 40, cy), self.font, 0.5, (0, 0, 0), 2)

#                     # 保存原始区域mask
#                     cv2.imwrite(f"{save_dir}/{room_type}_{room_idx}.png", (mask_filled.astype(np.uint8) * 255))
#                     room_idx += 1
#             else:
#                 # 没有餐厅子区域，正常处理整个区域
#                 mask_filled = binary_fill_holes(mask)
#                 mask_uint8 = (mask_filled * 255).astype(np.uint8)
#                 contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
#                 filled_mask = np.zeros_like(mask_uint8)
#                 cv2.drawContours(filled_mask, contours, -1, 255, thickness=-1)
#                 filled_mask_bool = filled_mask.astype(bool)

#                 color = self.room_color_map[room_type]
#                 color_img = np.zeros_like(image, dtype=np.uint8)
#                 color_img[:] = color
#                 blended = cv2.addWeighted(vis_image, 0.5, color_img, 0.7, 0)
#                 vis_image[filled_mask_bool] = blended[filled_mask_bool]

#                 color_mask[filled_mask_bool] = color

#                 ys, xs = np.where(filled_mask_bool)
#                 if len(xs) > 0 and len(ys) > 0:
#                     cx, cy = int(np.mean(xs)), int(np.mean(ys))
#                     cv2.putText(vis_image, room_type, (cx - 40, cy), self.font, 0.5, (0, 0, 0), 2)

#                 # 保存mask
#                 cv2.imwrite(f"{save_dir}/{room_type}_{room_idx}.png", (mask_filled.astype(np.uint8) * 255))
#                 room_idx += 1

#         # 绘制门框
#         for (x1, y1, x2, y2), cls in zip(boxes, classes):
#             if cls == 4:
#                 cv2.rectangle(vis_image, (x1, y1), (x2, y2), (255, 0, 255), 2)

#         # 绘制目标框和中心点
#         boxed_image = image.copy()
#         for (x1, y1, x2, y2), cls in zip(boxes, classes):
#             label = self.class_names.get(cls, str(cls))
#             color = self.object_color_map.get(label, (0, 255, 255))
#             cv2.rectangle(boxed_image, (x1, y1), (x2, y2), color, 2)
#             if label != "door":
#                 cx = (x1 + x2) // 2
#                 cy = (y1 + y2) // 2
#                 cv2.circle(boxed_image, (cx, cy), 3, (0, 0, 255), -1)
        
#         # 基于类别合并连通域
#         labels_im, room_type_for_label = self.bridge_merge_labels(labels_im, room_type_for_label)

#         # 重新计算区域面积
#         _, region_areas = self.merge_regions(labels_im, np.max(labels_im) + 1, boxes, classes)
        
#         # 拼接结果图
#         vis_h, vis_w = vis_image.shape[:2]
#         legend_height = 100
#         final_image = np.ones((vis_h + legend_height, vis_w * 2, 3), dtype=np.uint8) * 255
#         final_image[legend_height:, :vis_w] = vis_image
#         final_image[legend_height:, vis_w:] = boxed_image
#         cv2.line(final_image, (vis_w, legend_height), (vis_w, vis_h + legend_height), (255, 255, 255), 2)

#         # 添加图例
#         self._draw_legend(final_image, legend_height)

#         # 保存结果
#         cv2.imwrite(f"{save_dir}/overlay.png", vis_image)
#         cv2.imwrite(f"{save_dir}/mask_color.png", color_mask)
#         cv2.imwrite(f"{save_dir}/boxed.png", boxed_image)
#         cv2.imwrite(f"{save_dir}/{img_name}_final_result.png", final_image)

#     def _draw_legend(self, final_image, legend_height):
#         legend_font = 0.45
#         y_offset_2 = 20
#         y_offset_3 = 50
#         y_offset_4 = 80
#         x_step = 150

#         for i, label in enumerate(["bed_grounded", "bed_highleg"]):
#             color = self.object_color_map[label]
#             x = 10 + i * x_step
#             cv2.rectangle(final_image, (x, y_offset_2 - 15), (x + 20, y_offset_2 + 5), color, -1)
#             cv2.putText(final_image, label, (x + 25, y_offset_2 + 2), self.font, legend_font, (0, 0, 0), 1)

#         for i, label in enumerate(["sofa_grounded", "sofa_highleg"]):
#             color = self.object_color_map[label]
#             x = 10 + i * x_step
#             cv2.rectangle(final_image, (x, y_offset_3 - 15), (x + 20, y_offset_3 + 5), color, -1)
#             cv2.putText(final_image, label, (x + 25, y_offset_3 + 2), self.font, legend_font, (0, 0, 0), 1)

#         for i, label in enumerate(["door", "dining_table_set"]):
#             color = self.object_color_map[label]
#             x = 10 + i * x_step
#             cv2.rectangle(final_image, (x, y_offset_4 - 15), (x + 20, y_offset_4 + 5), color, -1)
#             cv2.putText(final_image, label, (x + 25, y_offset_4 + 2), self.font, legend_font, (0, 0, 0), 1)

#     def process_image(self, image_path, label_path):
#         img_name = Path(image_path).stem
#         image = cv2.imread(image_path)
#         if image is None:
#             print(f"⚠️ Failed to load image: {image_path}")
#             return
            
#         gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
#         h, w = gray.shape

#         boxes, classes = self.load_yolo_labels(label_path, w, h)

#         # 改进的门框处理：只填充门框内部，保留边界
#         _, binary = cv2.threshold(gray, 240, 255, cv2.THRESH_BINARY)
        
#         for (x1, y1, x2, y2), cls in zip(boxes, classes):
#             if cls == 4:  # door
#                 # 只填充内部，保留1像素边界
#                 cv2.rectangle(binary, (x1+1, y1+1), (x2-1, y2-1), 0, -1)
#             elif cls in [0, 1, 2, 3, 5]:
#                 cv2.rectangle(binary, (x1, y1), (x2, y2), 255, -1)

#         num_labels, labels_im = cv2.connectedComponents(binary)
        
#         # 改进的合并：保护餐厅区域
#         merged_labels, region_areas = self.merge_regions(labels_im, num_labels, boxes, classes)

#         save_dir = os.path.join(self.output_dir, img_name)
#         os.makedirs(save_dir, exist_ok=True)
#         self.classify_and_visualize(image, merged_labels, boxes, classes, region_areas, save_dir, img_name)
#         print(f"✅ Processed {img_name}, Saved to {save_dir}")

#     def batch_process(self):
#         os.makedirs(self.output_dir, exist_ok=True)
#         for img_file in sorted(os.listdir(self.image_dir)):
#             if not img_file.lower().endswith((".jpg", ".png")):
#                 continue
                
#             img_path = os.path.join(self.image_dir, img_file)
#             label_path = os.path.join(self.label_dir, Path(img_file).with_suffix(".txt"))
            
#             if not os.path.exists(label_path):
#                 print(f"⚠️ Label not found for {img_file}")
#                 continue
                
#             self.process_image(img_path, label_path)


# if __name__ == "__main__":
#     IMAGE_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/salm_0617/images/val"
#     LABEL_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/salm_0617/labels/val"
    
#     # IMAGE_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/test"
#     # LABEL_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/test"
    
#     OUTPUT_DIR = "output"
#     AREA_THRESH = 100

#     segmenter = RoomSegmenter(IMAGE_DIR, LABEL_DIR, OUTPUT_DIR, AREA_THRESH)
#     segmenter.batch_process()