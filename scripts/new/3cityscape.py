import os
import shutil
from pathlib import Path

# 输入输出路径
SRC_DIR = "output"  # 每张图像一个子目录，里面有 leftImg8bit.png 和 labelIds.png
DST_ROOT = "cityscapes_format"
SET_NAME = "train"
CITY_NAME = "myroom"

def ensure_dirs():
    img_dir = os.path.join(DST_ROOT, "leftImg8bit", SET_NAME, CITY_NAME)
    label_dir = os.path.join(DST_ROOT, "gtFine", SET_NAME, CITY_NAME)
    os.makedirs(img_dir, exist_ok=True)
    os.makedirs(label_dir, exist_ok=True)
    return img_dir, label_dir

def organize():
    img_dst_dir, label_dst_dir = ensure_dirs()

    for folder in sorted(os.listdir(SRC_DIR)):
        folder_path = os.path.join(SRC_DIR, folder)
        if not os.path.isdir(folder_path):
            continue

        img_file = os.path.join(folder_path, f"{folder}_leftImg8bit.png")
        label_file = os.path.join(folder_path, f"{folder}_labelIds.png")

        if not os.path.exists(img_file) or not os.path.exists(label_file):
            print(f"⚠️ 缺少图像或标签: {folder}")
            continue

        shutil.copy(img_file, os.path.join(img_dst_dir, f"{folder}_leftImg8bit.png"))
        shutil.copy(label_file, os.path.join(label_dst_dir, f"{folder}_labelIds.png"))

        print(f"✅ 已处理: {folder}")

if __name__ == "__main__":
    organize()
