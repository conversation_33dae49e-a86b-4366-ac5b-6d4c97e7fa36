
import os
import cv2
import numpy as np
from ultralytics import Y<PERSON><PERSON>

def load_colors_and_classnames(model):
    class_names = model.names
    fixed_colors = [
        (255, 0, 0), (0, 255, 0), (0, 128, 255), (255, 255, 0),
        (255, 0, 255), (0, 255, 255), (128, 0, 128), (0, 0, 255),
        (128, 128, 0), (0, 128, 0), (128, 0, 0), (0, 0, 128)
    ]
    colors = {cls_id: fixed_colors[cls_id % len(fixed_colors)] for cls_id in class_names}
    return class_names, colors

def draw_legend(canvas, class_names, colors, font_scale=0.3, thickness=1, box_size=10, padding=5):
    for idx, (cls_id, cls_name) in enumerate(class_names.items()):
        color = colors[cls_id]
        y = padding + idx * (box_size + 5)
        x = padding
        cv2.rectangle(canvas, (x, y), (x + box_size, y + box_size), color, -1)
        cv2.putText(canvas, cls_name, (x + box_size + 10, y + box_size - 2),
                    cv2.FONT_HERSHEY_SIMPLEX, font_scale, (0, 0, 0), thickness)

def draw_predictions(img, result, colors, font_scale, font_thickness, box_thickness):
    pred_img = img.copy()
    for box in result.boxes:
        xyxy = box.xyxy[0].cpu().numpy().astype(int)
        cls_id = int(box.cls[0])
        conf = box.conf[0].cpu().item()
        color = colors[cls_id]
        x1, y1, x2, y2 = xyxy
        cv2.rectangle(pred_img, (x1, y1), (x2, y2), color, box_thickness)
        text = f"{conf:.2f}"
        (tw, th), _ = cv2.getTextSize(text, cv2.FONT_HERSHEY_SIMPLEX, font_scale, font_thickness)
        cv2.rectangle(pred_img, (x1, y1 - th - 5), (x1 + tw, y1), color, -1)
        cv2.putText(pred_img, text, (x1, y1 - 5),
                    cv2.FONT_HERSHEY_SIMPLEX, font_scale, (0, 0, 0), font_thickness)
    return pred_img

def draw_ground_truth(img, label_path, class_names, colors, font_scale, font_thickness, box_thickness):
    gt_img = img.copy()
    h, w = gt_img.shape[:2]
    if not os.path.exists(label_path):
        return gt_img
    with open(label_path, "r") as f:
        for line in f:
            cls_id, x, y, bw, bh = map(float, line.strip().split())
            cls_id = int(cls_id)
            color = colors[cls_id]
            cx, cy = x * w, y * h
            bw, bh = bw * w, bh * h
            x1, y1 = int(cx - bw / 2), int(cy - bh / 2)
            x2, y2 = int(cx + bw / 2), int(cy + bh / 2)
            cv2.rectangle(gt_img, (x1, y1), (x2, y2), color, box_thickness)
            text = class_names[cls_id]
            (tw, th), _ = cv2.getTextSize(text, cv2.FONT_HERSHEY_SIMPLEX, font_scale, font_thickness)
            cv2.rectangle(gt_img, (x1, y1 - th - 5), (x1 + tw, y1), color, -1)
            cv2.putText(gt_img, text, (x1, y1 - 5),
                        cv2.FONT_HERSHEY_SIMPLEX, font_scale, (0, 0, 0), font_thickness)
    return gt_img

def process_image(result, label_dir, class_names, colors, save_path,
                  font_scale=0.3, font_thickness=1, box_thickness=1):
    img = result.orig_img
    filename = os.path.basename(result.path)
    label_path = os.path.join(label_dir, filename.replace(".jpg", ".txt").replace(".png", ".txt"))

    pred_img = draw_predictions(img, result, colors, font_scale, font_thickness, box_thickness)
    gt_img = draw_ground_truth(img, label_path, class_names, colors, font_scale, font_thickness, box_thickness)

    combined = np.hstack((pred_img, gt_img))
    h, w = combined.shape[:2]

    legend_height = len(class_names) * 15 + 5
    # 文字区高度，放在图例下方
    text_height = 5

    # 总画布高 = 图例 + 文字 + 拼接图
    canvas_height = legend_height + text_height + h
    canvas_width = w
    canvas = np.ones((canvas_height, canvas_width, 3), dtype=np.uint8) * 255

    # 画图例区域（顶部）
    legend_canvas = canvas[0:legend_height, :, :]
    draw_legend(legend_canvas, class_names, colors, font_scale, font_thickness)

    # # 画文字区域（图例下方）
    # font = cv2.FONT_HERSHEY_SIMPLEX
    # text_color = (0, 0, 0)
    # pred_text = "Prediction"
    # gt_text = "Ground Truth"
    # # 计算文字位置
    # (tw_pred, th), _ = cv2.getTextSize(pred_text, font, 0.6, 2)
    # (tw_gt, _), _ = cv2.getTextSize(gt_text, font, 0.6, 2)
    # pred_x = w // 4 - tw_pred // 2
    # gt_x = w * 3 // 4 - tw_gt // 2
    # text_y = legend_height + int(text_height * 0.7)  # 稍微往下点，接近文字底部

    # cv2.putText(canvas, pred_text, (pred_x, text_y), font, 0.6, text_color, 2)
    # cv2.putText(canvas, gt_text, (gt_x, text_y), font, 0.6, text_color, 2)

    # 放置拼接图（底部）
    canvas[legend_height + text_height:legend_height + text_height + h, :, :] = combined

    # 画分割线（从文字区下方开始，直到底部）
    line_x = w // 2
    cv2.line(canvas, (line_x, legend_height + text_height), (line_x, canvas_height), (255, 255, 255), 2)

    cv2.imwrite(save_path, canvas)
    print(f"✅ Saved: {save_path}")


def visualize_predictions_vs_ground_truth(model_path, image_dir, label_dir, save_dir):
    os.makedirs(save_dir, exist_ok=True)
    model = YOLO(model_path)
    class_names, colors = load_colors_and_classnames(model)

    results = model.predict(image_dir, save=False, show=False, conf=0.2)
    for result in results:
        filename = os.path.basename(result.path)
        save_path = os.path.join(save_dir, filename)
        process_image(result, label_dir, class_names, colors, save_path)


visualize_predictions_vs_ground_truth(
    model_path="/home/<USER>/panpan/code/ultralytics-main/runs/detect/train/weights/best.pt",
    image_dir="/home/<USER>/panpan/code/ultralytics-main/datasets/salm_0617/images/val",
    label_dir="/home/<USER>/panpan/code/ultralytics-main/datasets/salm_0617/labels/val",
    save_dir="/home/<USER>/panpan/code/ultralytics-main/runs/detect/train_predict_compare_conf0.2"
)
